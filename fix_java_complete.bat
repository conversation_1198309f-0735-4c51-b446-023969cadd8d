@echo off
echo Fixing Java PATH and Environment...

REM Fix PATH for current session
set "PATH=C:\Program Files\Java\jdk-17\bin;%PATH%"
set "JAVA_HOME=C:\Program Files\Java\jdk-17"

echo Testing Java version in current session...
java -version

echo.
echo If you see Java 17 above, run your program now: java hello
echo.
echo For permanent fix, please:
echo 1. Close this window
echo 2. Right-click PowerShell and "Run as administrator"
echo 3. Run the commands shown below:
echo.
echo [Environment]::SetEnvironmentVariable("PATH", "C:\Program Files\Java\jdk-17\bin;" + [Environment]::GetEnvironmentVariable("PATH", "Machine"), "Machine")
echo [Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Java\jdk-17", "Machine")
echo.
pause
